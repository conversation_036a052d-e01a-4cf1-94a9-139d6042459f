auth_enabled: false

server:
  http_listen_port: 3100

common:
  path_prefix: /loki
  ring:
    instance_addr: 127.0.0.1
    kvstore:
      store: inmemory
  replication_factor: 1

schema_config:
  configs:
    - from: "2024-01-01"
      store: tsdb # TSDB is the recommended index
      object_store: s3
      schema: v13 # v13 is current recommended schema
      index:
        prefix: index_
        period: 24h

storage_config:
  tsdb_shipper:
    active_index_directory: /loki/index
    cache_location: /loki/index_cache
  aws:
    bucketnames: loki
    endpoint: http://minio:9000
    region: us-east-1
    access_key_id: minioadmin
    secret_access_key: your_secure_minio_password_here
    s3forcepathstyle: true
    insecure: true # switch to false when you enable TLS on MinIO
